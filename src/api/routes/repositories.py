from typing import <PERSON><PERSON>

from blitzy_utils.logger import logger
from common_models.models import VersionControlSystem
from flask import Blueprint, request
from flask_utils.decorators import flask_pydantic_response
from flask_utils.models_config.model_utils import map_to_model

from src.api.models import GetGithubInstallationOutput, RepositoryMetadata
from src.azure.azure_app_service import AzureAppService
from src.error.errors import (AzureBaseError, InstallationNotFoundError,
                              ResourceNotFound, TokenExpiredError)
from src.github.github_app_service import GithubAppService
from src.service.azure_service import fetch_azure_secret
from src.service.git_installation_service import get_active_installation_by_target_id
from src.service.github_installation_access_service import (
    fetch_github_secret,
    get_active_github_installation_by_github_project_repo_id,
    get_active_github_installation_by_repo_id)
from src.service.github_integration_service import \
    get_github_installation_by_target_id
from src.service.github_project_repo_service import (
    get_github_project_repo_by_project_repo_id,
    get_github_project_repo_by_repo_id)

repositories_bp = Blueprint("repositories", __name__, url_prefix="/repositories")


@repositories_bp.route("/<repo_id>/installation", methods=["GET"])
@flask_pydantic_response
def get_installation_info_by_repository_id(repo_id: str):
    """Get the github repository by project id. This API explicitly checks
    for the existence of the repository in the database and then only returns the installation info
    """
    project_github_repo = get_github_project_repo_by_repo_id(repo_id)
    if not project_github_repo:
        raise ResourceNotFound(f"Repository with id {repo_id} not found in the database.")

    github_installation = get_github_installation_by_target_id(project_github_repo.org_name)
    if not github_installation:
        raise ResourceNotFound(f"Active installation for repository with id {repo_id} not found.")

    response = map_to_model(github_installation, GetGithubInstallationOutput)
    return response, 200


# ------------------------------------------------------------------------------
# Secret Fetching Functions and Endpoint by SCM Type
# ------------------------------------------------------------------------------


# Mapping of supported Source Control Management (SCM) systems to their respective
# secret-fetching functions. This is used to dynamically delegate the logic for
# retrieving access tokens or secrets based on the repository's configured SCM type.
#
# Keys:
#   - VersionControlSystem.GITHUB: GitHub-hosted repositories
#   - VersionControlSystem.AZURE_DEVOPS: Azure DevOps-hosted repositories
#
# Values:
#   - fetch_github_secret: function to fetch GitHub secrets
#   - fetch_azure_secret: function to fetch Azure DevOps secrets
fetch_secret_by_scm = {
    VersionControlSystem.GITHUB: fetch_github_secret,
    VersionControlSystem.AZURE_DEVOPS: fetch_azure_secret,
}


@repositories_bp.route("/<repo_id>/secret", methods=["GET"])
@flask_pydantic_response
def get_repository_secret(repo_id: str):
    """
    Retrieve an SCM access token for the given repository ID.

    This endpoint determines the Source Code Management (SCM) provider (e.g., GitHub or Azure DevOps)
    associated with the given repository ID, then delegates the token-fetching logic to the appropriate
    implementation based on the SCM type.

    Supported SCMs:
    - GitHub
    - Azure DevOps

    :param repo_id: The unique identifier of the repository.
    :return: A SecretsOutput object containing the access token and SCM type, returned with HTTP 200.
    :raises ResourceNotFound:
        - If the repository is not found.
        - If no active installation or tenant context is found.
        - If the access token could not be retrieved from the SCM provider.
    :raises ValueError: If the SCM type is not supported or improperly configured.
    :raises TokenExpiredError: If the SCM token is expired and cannot be refreshed.
    :raises AzureBaseError: If there's an error retrieving the access token from Azure DevOps.
    """
    logger.info(f"[AccessToken] Fetching SCM access token for repo_id: {repo_id}")

    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        logger.error(f"[AccessToken] No active installation found for repo_id: {repo_id}")
        raise ResourceNotFound(f"Project Repository SCM Field with repo_id {repo_id} not found in the database.")

    logger.info(f"[AccessToken] Found active installation for repo_id: {repo_id}, svc_type: {installation.svc_type}")

    fetch_token_fn = fetch_secret_by_scm.get(installation.svc_type)
    if not fetch_token_fn:
        logger.error(f"[AccessToken] Unsupported SCM type '{installation.svc_type}' for repo_id: {repo_id}")
        raise ValueError(f"Unsupported SCM type: {installation.svc_type}.")

    try:
        token_data = fetch_token_fn(installation.installation_id)
    except TokenExpiredError:
        raise
    except Exception as e:
        logger.error(
            f"[AccessToken] Error retrieving access token for repo_id: {repo_id} "
            f"from {installation.svc_type}: {str(e)}"
        )
        raise AzureBaseError(
            f"[AccessToken] Error retrieving access token for repo_id: {repo_id} from {installation.svc_type}"
        )

    if not token_data:
        logger.error(
            f"[AccessToken] Access token for repo_id '{repo_id}' could not be retrieved from {installation.svc_type}"
        )
        raise ResourceNotFound(
            f"Access token for repo_id '{repo_id}' could not be retrieved from {installation.svc_type}."
        )

    logger.info(f"[AccessToken] Successfully retrieved access token for repo_id: {repo_id}")
    return token_data, 200


@repositories_bp.route("/<project_repo_id>/content/<path:file_path>", methods=["GET"])
@flask_pydantic_response
def get_file_content(project_repo_id: str, file_path: str) -> Tuple[dict, int]:
    """
    Retrieve file content at a specific commit, branch, or tag.
    :param project_repo_id: The unique identifier of the repository.
    :param file_path: The path to the file in the repository (from URL).
    :query ref: The commit SHA, branch, or tag (optional, as a query parameter).
    :return: A dictionary with file content and metadata, returned with HTTP 200.
    :raises ResourceNotFound: If the file or repository is not found.
    """

    # Get query parameters
    ref = request.args.get("ref")  # Optional: branch, tag, or commit SHA

    # Validate file_path
    if not file_path or not file_path.strip():
        logger.error(f"[Content] Missing or empty required file_path query parameter "
                     f"for project_repo_id: {project_repo_id}")
        raise ResourceNotFound("file_path is a required query parameter and cannot be empty.")

    # Fetch installation for the repository
    installation = get_active_github_installation_by_github_project_repo_id(project_repo_id)
    if not installation:
        logger.error(f"[Content] Active installation not found for project_repo_id: {project_repo_id}")
        raise InstallationNotFoundError(message=f"Active Installation for repo {project_repo_id} not found.")

    # Fetch repository details
    repo_details = get_github_project_repo_by_project_repo_id(project_repo_id)
    if not repo_details:
        logger.error(f"[Content] Repository details not found for project_repo_id: {project_repo_id}")
        raise ResourceNotFound(f"Repository details not found for project_repo_id: {project_repo_id}")

    logger.info(f"[Content] Fetching file '{file_path}' from repo_id: {project_repo_id} at ref: {ref}")

    svc = getattr(installation, "svc_type", "")
    iid = str(getattr(installation, "installation_id", ""))

    if not svc or not iid:
        logger.error(f"[Content] Missing or empty installation details for project_repo_id: {project_repo_id}")
        raise ResourceNotFound(f"Installation details not found for project_repo_id: {project_repo_id}.")

    if svc == VersionControlSystem.GITHUB:
        # Handle GitHub repositories
        github_service = GithubAppService()
        logger.info(f"Github installation {iid} found for {project_repo_id}, getting file content")
        try:
            repo_id_str = str(getattr(repo_details, "repo_id", ""))
            file_content = github_service.get_file_content_by_path(
                iid, repo_id_str, file_path, ref or ""
            )
            if not file_content:
                repo_name = getattr(repo_details, "repo_name", "unknown")
                logger.warning(f"[Content] File '{file_path}' not found in repository '{repo_name}' at ref '{ref}'")
                raise ResourceNotFound(f"File '{file_path}' not found in repository '{repo_name}' at ref '{ref}'.")
        except Exception as e:
            logger.error(f"[Content] Exception getting '{file_path}' "
                         f"from repo_id: {project_repo_id} at ref: {ref}: {str(e)}")
            raise ResourceNotFound(f"Error retrieving file '{file_path}' from '{project_repo_id}' at ref '{ref}'.")

    elif svc == VersionControlSystem.AZURE_DEVOPS:
        # Handle Azure DevOps repositories
        instance = AzureAppService()
        logger.info(f"Azure installation {iid} found for repo {project_repo_id}, getting file content")

        # Check required Azure fields
        required_fields = ["repo_id", "azure_project_id", "azure_org_id"]
        missing_fields = [field for field in required_fields if not getattr(repo_details, field, None)]
        if missing_fields:
            logger.error(f"[Content] Missing Azure installation fields "
                         f"for project_repo_id: {project_repo_id}: {missing_fields}")
            raise ResourceNotFound(f"Missing required Azure installation fields: {', '.join(missing_fields)}")

        repo_id = str(getattr(repo_details, "repo_id", ""))
        azure_project_id = str(getattr(repo_details, "azure_project_id", ""))
        azure_organization_id = str(getattr(repo_details, "azure_org_id", ""))

        logger.info(
            f"[Content] Azure repo details for project_repo_id: {project_repo_id} - "
            f"repo_id: {repo_id}, azure_project_id: {azure_project_id}, azure_org_id: {azure_organization_id}"
        )
        try:
            file_content = instance.get_file_content_by_path(
                str(installation.installation_id),
                azure_organization_id,
                azure_project_id,
                repo_id,
                file_path,
                ref or ""
            )
            if not file_content:
                logger.warning(f"[Content] File '{file_path}' not found in Azure repository '{repo_id}' at ref '{ref}'")
                raise ResourceNotFound(f"[Azure] File '{file_path}' not found for '{repo_id}' at ref '{ref}'.")
        except Exception as e:
            logger.error(f"[Content] Exception getting '{file_path}' from repo_id: {repo_id} at ref: {ref}: {str(e)}")
            raise ResourceNotFound(f"Error getting '{file_path}' from repo: '{repo_id}' at ref '{ref}'.") from e
    else:
        # Handle unsupported SCM types
        logger.error(f"[Content] Unsupported SCM type '{svc}' for project repo id: {project_repo_id}")
        raise ResourceNotFound(f"Unknown SVC type for installation {iid}: {svc}.")

    logger.info(f"[Content] Successfully retrieved file '{file_path}' "
                f"from project_repo_id: {project_repo_id} at ref: {ref}")
    # Return file content as a serializable dict
    try:
        result = file_content.to_dict()
    except ValueError as e:
        logger.error(f"[Content] Error converting file content to dict: {str(e)}")
        raise ResourceNotFound(f"Error converting file content to dict for '{file_path}' in '{project_repo_id}'.")
    return result, 200


@repositories_bp.route("/<repo_id>", methods=["GET"])
@flask_pydantic_response
def get_repository_by_id(repo_id: str):
    repo = get_github_project_repo_by_repo_id(repo_id)
    if not repo:
        raise ResourceNotFound(f"Repository with id {repo} not found in the database.")

    org_info = get_active_installation_by_target_id(repo.org_id)
    if not org_info:
        raise ResourceNotFound(f"Active installation for repository with id {repo} not found.")

    metadata = map_to_model(repo, RepositoryMetadata)
    metadata.installationType = org_info.installation_type

    return metadata, 200
